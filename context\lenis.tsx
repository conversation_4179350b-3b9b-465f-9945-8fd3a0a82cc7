"use client";

import gsap from "gsap";
import { useEffect, useRef } from "react";
import React<PERSON>enis, { LenisRef } from "lenis/react";

export function LenisProvider() {
  const lenisRaf = useRef<LenisRef>(null);

  useEffect(() => {
    function update(time: number) {
      lenisRaf.current?.lenis?.raf(time);
    }

    gsap.ticker.add(update);

    return () => {
      gsap.ticker.remove(update);
    };
  }, []);

  return <ReactLenis root options={{ autoRaf: false }} ref={lenisRaf} />;
}
