import {
  <PERSON><PERSON><PERSON>_Mono as <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from "next/font/google";

import local from "next/font/local";

import { cn } from "@/lib/utils";

const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-geist-sans",
});

const fontMono = FontMono({
  subsets: ["latin"],
  variable: "--font-geist-mono",
  weight: ["400"],
});

const fontInter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const humane = local({
  src: [
    {
      path: "../public/fonts/humane/Humane-Thin.woff2",
      weight: "100",
      style: "normal",
    },
    {
      path: "../public/fonts/humane/Humane-Light.woff2",
      weight: "300",
      style: "normal",
    },
    {
      path: "../public/fonts/humane/Humane-Regular.woff2",
      weight: "400",
      style: "normal",
    },
    {
      path: "../public/fonts/humane/Humane-Medium.woff2",
      weight: "500",
      style: "normal",
    },
    {
      path: "../public/fonts/humane/Humane-Bold.woff2",
      weight: "700",
      style: "normal",
    },
    {
      path: "../public/fonts/humane/Humane-Black.woff2",
      weight: "900",
      style: "normal",
    },

  ],
  variable: "--font-humane",
});

export const fontVariables = cn(
  fontSans.variable,
  fontMono.variable,
  fontInter.variable,
  humane.variable,
);
