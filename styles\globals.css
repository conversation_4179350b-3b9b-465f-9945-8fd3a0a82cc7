@import "tailwindcss";
@import "tw-animate-css";

@import "./lenis.css";

:root {
  --primary: oklch(0.448 0.197 265.96);
  --background: oklch(0.142 0.003 17.58);
  --foreground: oklch(0.876 0 0);
  --muted: oklch(0.640 0 0);
  --border: oklch(0.389 0.013 285.76);
  --radius: 0.625rem;
}

@theme inline {
  --breakpoint-3xl: 1600px;
  --breakpoint-4xl: 2000px;
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-primary: var(--primary);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-muted: var(--muted);
  --color-border: var(--border);

  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-inter: var(--font-inter);
  --font-humane: var(--font-humane);
}

@layer base {
  body {
    background: var(--background);
    color: var(--foreground);
    font-family: var(--font-sans), Arial, Helvetica, sans-serif;
  }
}
