{"name": "next-portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@gsap/react": "^2.1.2", "@next/third-parties": "^15.3.3", "@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "clsx": "^2.1.1", "gsap": "^3.13.0", "lenis": "^1.3.4", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.0", "three": "^0.177.0", "zod": "^3.25.51"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/three": "^0.177.0", "eslint": "^9", "eslint-config-next": "15.3.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "tailwind-clamp": "^4.0.18", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}