import type { Metada<PERSON> } from "next";
import { GoogleAnalytics, GoogleTagManager } from "@next/third-parties/google";

import "@/styles/globals.css";

import { cn } from "@/lib/utils";
import { fontVariables } from "@/lib/fonts";
import { siteConfig } from "@/lib/config";

const BASE_URL = process.env.NEXT_PUBLIC_APP_URL!;
const CREATOR = process.env.NEXT_PUBLIC_APP_NAME!;

export const metadata: Metadata = {
  title: {
    default: siteConfig.name,
    template: `%s — ${siteConfig.name}`,
  },
  description: siteConfig.description,
  metadataBase: new URL(BASE_URL),
  keywords: siteConfig.keywords,
  authors: [{ name: CREATOR, url: BASE_URL }],
  creator: CREATOR,
  openGraph: {
    type: "website",
    locale: "en_US",
    url: BASE_URL,
    siteName: siteConfig.name,
    title: siteConfig.name,
    images: [],
  },
  twitter: {
    card: "summary_large_image",
    title: siteConfig.name,
    description: siteConfig.description,
    images: [`${process.env.NEXT_PUBLIC_APP_URL}/opengraph-image.png`],
    creator: "@yovizn_",
  },
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon-16x16.png",
    apple: "/apple-touch-icon.png",
  },
  manifest: `${BASE_URL}/site.webmanifest}`,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const isProduction = process.env.NODE_ENV === "production";

  return (
    <html lang="en" suppressHydrationWarning>
      {isProduction && (
        <GoogleTagManager gtmId={process.env.NEXT_PUBLIC_GTM_ID!} />
      )}
      <head>
        <meta name="apple-mobile-web-app-title" content="yovizn" />
      </head>
      <body
        className={cn(
          "text-foreground bg-background group/body overscroll-none font-sans",
          fontVariables,
        )}
        style={{ overflow: "auto", scrollbarWidth: "none" }}
      >
        {isProduction && (
          <noscript>
            <iframe
              src={`https://www.googletagmanager.com/ns.html?id=${env.NEXT_PUBLIC_GTM_ID}`}
              height="0"
              width="0"
              style={{ display: "none", visibility: "hidden" }}
            />
          </noscript>
        )}
        {children}
      </body>

      {isProduction && (
        <GoogleAnalytics gaId={process.env.NEXT_PUBLIC_GA_ID!} />
      )}
    </html>
  );
}
